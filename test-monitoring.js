/**
 * 心拍数監視システムのテストスクリプト
 * Discord通知機能とスパム対策の動作確認用
 */

const { heartRateMonitor } = require('./src/services/heartRateMonitor');
const { spamProtection } = require('./src/services/spamProtection');
const { 
    getDetailedStatus, 
    getSimpleStatus, 
    validateConfiguration,
    logCurrentStatus 
} = require('./src/utils/monitoringUtils');
const { config } = require('./src/config');

/**
 * 設定確認
 */
function checkConfiguration() {
    console.log("=== Configuration Check ===");
    const validation = validateConfiguration();
    
    console.log(`Configuration is ${validation.isValid ? 'VALID' : 'INVALID'}`);
    
    if (validation.issues.length > 0) {
        console.log("\nISSUES:");
        validation.issues.forEach(issue => console.log(`  ❌ ${issue}`));
    }
    
    if (validation.warnings.length > 0) {
        console.log("\nWARNINGS:");
        validation.warnings.forEach(warning => console.log(`  ⚠️  ${warning}`));
    }
    
    console.log("\nCurrent Configuration:");
    console.log(`  Heart Rate Threshold: ${validation.configuration.threshold} bpm`);
    console.log(`  Required Duration: ${validation.configuration.duration / 1000}s`);
    console.log(`  Notification Cooldown: ${validation.configuration.cooldown / 1000}s`);
    console.log(`  Max Notifications/Hour: ${validation.configuration.maxPerHour}`);
    console.log(`  Discord Configured: ${config.DISCORD_WEBHOOK_URL && config.DISCORD_WEBHOOK_URL !== "YOUR_DISCORD_WEBHOOK_URL_HERE" ? 'YES' : 'NO'}`);
    console.log("============================\n");
}

/**
 * 心拍数データのシミュレーション
 */
async function simulateHeartRateData() {
    console.log("=== Heart Rate Simulation ===");
    
    // 正常な心拍数
    console.log("Simulating normal heart rate...");
    await heartRateMonitor.processHeartRate(75);
    await heartRateMonitor.processHeartRate(80);
    await heartRateMonitor.processHeartRate(78);
    
    logCurrentStatus();
    
    // 高心拍数の開始
    console.log("Simulating high heart rate...");
    await heartRateMonitor.processHeartRate(120);
    await heartRateMonitor.processHeartRate(125);
    await heartRateMonitor.processHeartRate(130);
    
    logCurrentStatus();
    
    // 高心拍数の継続（通知トリガー用）
    console.log("Simulating prolonged high heart rate...");
    
    // 時間を進めるために、開始時刻を過去に設定
    const pastTime = new Date(Date.now() - config.HIGH_HEART_RATE_DURATION - 10000);
    heartRateMonitor.highHeartRateStartTime = pastTime;
    
    await heartRateMonitor.processHeartRate(135);
    
    logCurrentStatus();
    
    // 正常化
    console.log("Simulating heart rate normalization...");
    await heartRateMonitor.processHeartRate(85);
    await heartRateMonitor.processHeartRate(80);
    
    logCurrentStatus();
    
    console.log("==============================\n");
}

/**
 * スパム対策のテスト
 */
async function testSpamProtection() {
    console.log("=== Spam Protection Test ===");
    
    const now = new Date();
    
    // 通知可能性チェック
    console.log("Testing notification availability...");
    const canSend1 = spamProtection.canSendNotification('high_heart_rate', now);
    console.log(`Can send high_heart_rate notification: ${canSend1.allowed}`);
    if (!canSend1.allowed) {
        console.log(`  Reason: ${canSend1.reason}`);
    }
    
    // 通知記録
    if (canSend1.allowed) {
        console.log("Recording notification...");
        spamProtection.recordNotification('high_heart_rate', now);
    }
    
    // 即座に再度チェック（クールダウンテスト）
    console.log("Testing cooldown period...");
    const canSend2 = spamProtection.canSendNotification('high_heart_rate', now);
    console.log(`Can send notification immediately after: ${canSend2.allowed}`);
    if (!canSend2.allowed) {
        console.log(`  Reason: ${canSend2.reason}`);
        if (canSend2.remainingSeconds) {
            console.log(`  Remaining time: ${canSend2.remainingSeconds}s`);
        }
    }
    
    // 次回通知可能時刻
    const nextTime = spamProtection.getNextAvailableTime('high_heart_rate');
    console.log(`Next available time: ${nextTime ? nextTime.toLocaleString() : 'Now'}`);
    
    console.log("=============================\n");
}

/**
 * 状態表示のテスト
 */
function testStatusDisplay() {
    console.log("=== Status Display Test ===");
    
    console.log("Simple Status:");
    const simpleStatus = getSimpleStatus();
    console.log(JSON.stringify(simpleStatus, null, 2));
    
    console.log("\nDetailed Status:");
    const detailedStatus = getDetailedStatus();
    console.log(JSON.stringify(detailedStatus, null, 2));
    
    console.log("============================\n");
}

/**
 * メイン実行関数
 */
async function main() {
    console.log("Heart Rate Monitoring System Test\n");
    
    try {
        // 設定確認
        checkConfiguration();
        
        // 心拍数シミュレーション
        await simulateHeartRateData();
        
        // スパム対策テスト
        await testSpamProtection();
        
        // 状態表示テスト
        testStatusDisplay();
        
        console.log("✅ All tests completed successfully!");
        
    } catch (error) {
        console.error("❌ Test failed:", error);
    }
}

// スクリプトが直接実行された場合のみテストを実行
if (require.main === module) {
    main();
}

module.exports = {
    checkConfiguration,
    simulateHeartRateData,
    testSpamProtection,
    testStatusDisplay,
    main
};
