hoistPattern:
  - '*'
hoistedDependencies:
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  bintrees@1.0.2:
    bintrees: private
  tdigest@0.1.2:
    tdigest: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Mon, 02 Jun 2025 07:50:31 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
