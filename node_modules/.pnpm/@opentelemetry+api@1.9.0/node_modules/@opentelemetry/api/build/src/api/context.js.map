{"version": 3, "file": "context.js", "sourceRoot": "", "sources": ["../../../src/api/context.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,sEAAmE;AAEnE,2DAIkC;AAClC,iCAAiC;AAEjC,MAAM,QAAQ,GAAG,SAAS,CAAC;AAC3B,MAAM,oBAAoB,GAAG,IAAI,uCAAkB,EAAE,CAAC;AAEtD;;GAEG;AACH,MAAa,UAAU;IAGrB,+FAA+F;IAC/F,gBAAuB,CAAC;IAExB,oDAAoD;IAC7C,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,EAAE,CAAC;SACnC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,cAA8B;QAC3D,OAAO,IAAA,6BAAc,EAAC,QAAQ,EAAE,cAAc,EAAE,cAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;;OAOG;IACI,IAAI,CACT,OAAgB,EAChB,EAAK,EACL,OAA8B,EAC9B,GAAG,IAAO;QAEV,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IACvE,CAAC;IAED;;;;;OAKG;IACI,IAAI,CAAI,OAAgB,EAAE,MAAS;QACxC,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACzD,CAAC;IAEO,kBAAkB;QACxB,OAAO,IAAA,wBAAS,EAAC,QAAQ,CAAC,IAAI,oBAAoB,CAAC;IACrD,CAAC;IAED,oDAAoD;IAC7C,OAAO;QACZ,IAAI,CAAC,kBAAkB,EAAE,CAAC,OAAO,EAAE,CAAC;QACpC,IAAA,+BAAgB,EAAC,QAAQ,EAAE,cAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjD,CAAC;CACF;AAnED,gCAmEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NoopContextManager } from '../context/NoopContextManager';\nimport { Context, ContextManager } from '../context/types';\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\nimport { DiagAPI } from './diag';\n\nconst API_NAME = 'context';\nconst NOOP_CONTEXT_MANAGER = new NoopContextManager();\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Context API\n */\nexport class ContextAPI {\n  private static _instance?: ContextAPI;\n\n  /** Empty private constructor prevents end users from constructing a new instance of the API */\n  private constructor() {}\n\n  /** Get the singleton instance of the Context API */\n  public static getInstance(): ContextAPI {\n    if (!this._instance) {\n      this._instance = new ContextAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Set the current context manager.\n   *\n   * @returns true if the context manager was successfully registered, else false\n   */\n  public setGlobalContextManager(contextManager: ContextManager): boolean {\n    return registerGlobal(API_NAME, contextManager, DiagAPI.instance());\n  }\n\n  /**\n   * Get the currently active context\n   */\n  public active(): Context {\n    return this._getContextManager().active();\n  }\n\n  /**\n   * Execute a function with an active context\n   *\n   * @param context context to be active during function execution\n   * @param fn function to execute in a context\n   * @param thisArg optional receiver to be used for calling fn\n   * @param args optional arguments forwarded to fn\n   */\n  public with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    context: Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F> {\n    return this._getContextManager().with(context, fn, thisArg, ...args);\n  }\n\n  /**\n   * Bind a context to a target function or event emitter\n   *\n   * @param context context to bind to the event emitter or function. Defaults to the currently active context\n   * @param target function or event emitter to bind\n   */\n  public bind<T>(context: Context, target: T): T {\n    return this._getContextManager().bind(context, target);\n  }\n\n  private _getContextManager(): ContextManager {\n    return getGlobal(API_NAME) || NOOP_CONTEXT_MANAGER;\n  }\n\n  /** Disable and remove the global context manager */\n  public disable() {\n    this._getContextManager().disable();\n    unregisterGlobal(API_NAME, DiagAPI.instance());\n  }\n}\n"]}