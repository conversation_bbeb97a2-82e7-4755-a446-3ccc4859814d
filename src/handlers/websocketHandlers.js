/**
 * WebSocketイベントハンドラー
 * Pulsoid WebSocket接続のイベント処理を管理
 */

const { parseHeartRateFromMessage } = require('../utils/messageParser');
const { sendHeartRateToPrometheus } = require('../services/prometheus');

/**
 * WebSocket接続が開かれた時のハンドラー
 */
function handleWebSocketOpen() {
    console.log("Connected to Pulsoid! Waiting for heart rate data...");
}

/**
 * WebSocketメッセージを受信した時のハンドラー
 * @param {Buffer} message - 受信したメッセージ
 */
async function handleWebSocketMessage(message) {
    try {
        const heartRate = parseHeartRateFromMessage(message);

        if (heartRate !== undefined) {
            console.log(`Processing Heart Rate: ${heartRate} bpm`);
            await sendHeartRateToPrometheus(heartRate);
        } else {
            console.log("Received a message, but could not extract heart rate from it.");
        }
    } catch (error) {
        console.error("An unexpected error occurred while processing the message:", error);
    }
}

/**
 * WebSocketエラーが発生した時のハンドラー
 * @param {Error} error - 発生したエラー
 * @param {WebSocket} ws - WebSocketインスタンス
 */
function handleWebSocketError(error, ws) {
    console.error("WebSocket Error:", error);
    ws.terminate();
}

/**
 * WebSocket接続が閉じられた時のハンドラー
 * @param {Function} reconnectCallback - 再接続用のコールバック関数
 */
function handleWebSocketClose(reconnectCallback) {
    console.log("WebSocket disconnected. Reconnecting in 5 seconds...");
    setTimeout(reconnectCallback, 5000);
}

module.exports = {
    handleWebSocketOpen,
    handleWebSocketMessage,
    handleWebSocketError,
    handleWebSocketClose
};
