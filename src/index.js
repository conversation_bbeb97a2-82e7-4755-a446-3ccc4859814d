/**
 * Pulsoid to Prometheus Bridge
 * アプリケーションのメインロジック
 */

const { validateConfig } = require('./config');
const { connectToPulsoid } = require('./services/pulsoidClient');

/**
 * アプリケーションの開始
 */
function main() {
    console.log("Starting Pulsoid to Prometheus Bridge...");
    
    // 設定の検証
    if (!validateConfig()) {
        console.error("Configuration validation failed. Exiting...");
        process.exit(1);
    }
    
    console.log("Configuration validated successfully.");
    
    // Pulsoid WebSocketに接続
    connectToPulsoid();
}

module.exports = {
    main
};
