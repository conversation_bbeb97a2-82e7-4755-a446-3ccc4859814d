// .envファイルから環境変数を読み込む
// ルートディレクトリの.envファイルを読み込むため、パスを指定
require("dotenv").config({ path: require('path').join(__dirname, '../../.env') });

/**
 * アプリケーション設定
 */
const config = {
    // PulsoidのWebSocket URL
    PULSOID_WSS_URL: process.env.PULSOID_WS_SERVER_URI,

    // VictoriaMetrics/PrometheusのURL
    VICTORIAMETRICS_URL: process.env.VICTORIAMETRICS_URL || "http://localhost:8428/write",

    // 再接続間隔（ミリ秒）
    RECONNECT_INTERVAL: 5000,

    // Discord通知設定
    DISCORD_WEBHOOK_URL: process.env.DISCORD_WEBHOOK_URL,

    // 心拍数監視設定
    HEART_RATE_THRESHOLD: parseInt(process.env.HEART_RATE_THRESHOLD) || 100, // 閾値（bpm）
    HIGH_HEART_RATE_DURATION: parseInt(process.env.HIGH_HEART_RATE_DURATION) || 300000, // 高心拍数継続時間（ミリ秒、デフォルト5分）

    // スパム対策設定
    NOTIFICATION_COOLDOWN: parseInt(process.env.NOTIFICATION_COOLDOWN) || 1800000, // 通知クールダウン時間（ミリ秒、デフォルト30分）
    MAX_NOTIFICATIONS_PER_HOUR: parseInt(process.env.MAX_NOTIFICATIONS_PER_HOUR) || 2 // 1時間あたりの最大通知数
};

/**
 * 設定の検証
 * @returns {boolean} 設定が有効かどうか
 */
function validateConfig() {
    if (!config.PULSOID_WSS_URL || config.PULSOID_WSS_URL.includes("YOUR_PULSOID_TOKEN")) {
        console.error("Error: Please set a valid PULSOID_WS_SERVER_URI in your .env file.");
        return false;
    }

    if (config.DISCORD_WEBHOOK_URL && !config.DISCORD_WEBHOOK_URL.startsWith("https://discord.com/api/webhooks/")) {
        console.error("Error: DISCORD_WEBHOOK_URL must be a valid Discord webhook URL.");
        return false;
    }

    if (config.HEART_RATE_THRESHOLD <= 0) {
        console.error("Error: HEART_RATE_THRESHOLD must be a positive number.");
        return false;
    }

    if (config.HIGH_HEART_RATE_DURATION <= 0) {
        console.error("Error: HIGH_HEART_RATE_DURATION must be a positive number.");
        return false;
    }

    return true;
}

module.exports = {
    config,
    validateConfig
};
