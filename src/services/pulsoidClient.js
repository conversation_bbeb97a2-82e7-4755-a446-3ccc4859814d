/**
 * Pulsoid WebSocketクライアント
 * Pulsoidサービスとの接続を管理
 */

const WebSocket = require("ws");
const { config } = require('../config');
const {
    handleWebSocketOpen,
    handleWebSocketMessage,
    handleWebSocketError,
    handleWebSocketClose
} = require('../handlers/websocketHandlers');

/**
 * PulsoidのWebSocketに接続し、データ受信を開始する関数
 */
function connectToPulsoid() {
    console.log("Connecting to Pulsoid WebSocket...");
    const ws = new WebSocket(config.PULSOID_WSS_URL);

    ws.on("open", handleWebSocketOpen);
    ws.on("message", handleWebSocketMessage);
    ws.on("error", (error) => handleWebSocketError(error, ws));
    ws.on("close", () => handleWebSocketClose(connectToPulsoid));
}

module.exports = {
    connectToPulsoid
};
