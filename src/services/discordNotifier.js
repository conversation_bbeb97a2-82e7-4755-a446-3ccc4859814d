/**
 * Discord通知サービス
 * Discord Webhookを使用して通知を送信する
 */

const { config } = require('../config');

/**
 * Discord Webhookに通知を送信する関数
 * @param {Object} options - 通知オプション
 * @param {number} options.heartRate - 現在の心拍数
 * @param {number} options.threshold - 閾値
 * @param {number} options.duration - 高心拍数継続時間（ミリ秒）
 * @param {Date} options.startTime - 高心拍数開始時刻
 */
async function sendDiscordNotification({ heartRate, threshold, duration, startTime }) {
    if (!config.DISCORD_WEBHOOK_URL || config.DISCORD_WEBHOOK_URL === "YOUR_DISCORD_WEBHOOK_URL_HERE") {
        console.log("Discord webhook URL not configured. Skipping notification.");
        return;
    }

    const durationMinutes = Math.round(duration / 60000);
    const startTimeStr = startTime.toLocaleString('ja-<PERSON>', { 
        timeZone: 'Asia/Tokyo',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const embed = {
        title: "🚨 高心拍数アラート",
        description: `心拍数が閾値を超えた状態が継続しています`,
        color: 0xFF0000, // 赤色
        fields: [
            {
                name: "現在の心拍数",
                value: `${heartRate} bpm`,
                inline: true
            },
            {
                name: "閾値",
                value: `${threshold} bpm`,
                inline: true
            },
            {
                name: "継続時間",
                value: `${durationMinutes}分`,
                inline: true
            },
            {
                name: "開始時刻",
                value: startTimeStr,
                inline: false
            }
        ],
        timestamp: new Date().toISOString(),
        footer: {
            text: "Pulsoid Heart Rate Monitor"
        }
    };

    const payload = {
        username: "Heart Rate Monitor",
        avatar_url: "https://cdn.discordapp.com/emojis/1234567890123456789.png", // オプション: アバター画像
        embeds: [embed]
    };

    try {
        const response = await fetch(config.DISCORD_WEBHOOK_URL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            throw new Error(`Discord webhook error! status: ${response.status}`);
        }

        console.log(`Discord notification sent successfully for heart rate ${heartRate} bpm`);
    } catch (error) {
        console.error("Error sending Discord notification:", error);
    }
}

/**
 * 心拍数正常化通知を送信する関数
 * @param {Object} options - 通知オプション
 * @param {number} options.heartRate - 現在の心拍数
 * @param {number} options.threshold - 閾値
 * @param {number} options.totalDuration - 総継続時間（ミリ秒）
 * @param {Date} options.startTime - 高心拍数開始時刻
 * @param {Date} options.endTime - 高心拍数終了時刻
 */
async function sendHeartRateNormalizedNotification({ heartRate, threshold, totalDuration, startTime, endTime }) {
    if (!config.DISCORD_WEBHOOK_URL || config.DISCORD_WEBHOOK_URL === "YOUR_DISCORD_WEBHOOK_URL_HERE") {
        console.log("Discord webhook URL not configured. Skipping notification.");
        return;
    }

    const durationMinutes = Math.round(totalDuration / 60000);
    const startTimeStr = startTime.toLocaleString('ja-JP', { 
        timeZone: 'Asia/Tokyo',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    const endTimeStr = endTime.toLocaleString('ja-JP', { 
        timeZone: 'Asia/Tokyo',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const embed = {
        title: "✅ 心拍数正常化",
        description: `心拍数が正常範囲に戻りました`,
        color: 0x00FF00, // 緑色
        fields: [
            {
                name: "現在の心拍数",
                value: `${heartRate} bpm`,
                inline: true
            },
            {
                name: "閾値",
                value: `${threshold} bpm`,
                inline: true
            },
            {
                name: "総継続時間",
                value: `${durationMinutes}分`,
                inline: true
            },
            {
                name: "開始時刻",
                value: startTimeStr,
                inline: true
            },
            {
                name: "終了時刻",
                value: endTimeStr,
                inline: true
            }
        ],
        timestamp: new Date().toISOString(),
        footer: {
            text: "Pulsoid Heart Rate Monitor"
        }
    };

    const payload = {
        username: "Heart Rate Monitor",
        embeds: [embed]
    };

    try {
        const response = await fetch(config.DISCORD_WEBHOOK_URL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            throw new Error(`Discord webhook error! status: ${response.status}`);
        }

        console.log(`Discord normalization notification sent successfully for heart rate ${heartRate} bpm`);
    } catch (error) {
        console.error("Error sending Discord normalization notification:", error);
    }
}

module.exports = {
    sendDiscordNotification,
    sendHeartRateNormalizedNotification
};
