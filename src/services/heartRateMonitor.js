/**
 * 心拍数監視サービス
 * 心拍数データを監視し、閾値を超えた時間を追跡する
 */

const { config } = require('../config');
const { sendDiscordNotification, sendHeartRateNormalizedNotification } = require('./discordNotifier');
const { spamProtection } = require('./spamProtection');

/**
 * 心拍数監視クラス
 */
class HeartRateMonitor {
    constructor() {
        // 高心拍数状態の追跡
        this.isHighHeartRate = false;
        this.highHeartRateStartTime = null;
        this.lastNotificationTime = null;
        
        // 削除: スパム対策はspamProtectionサービスで管理
        
        // 最後の心拍数値
        this.lastHeartRate = null;
        
        console.log(`Heart Rate Monitor initialized with threshold: ${config.HEART_RATE_THRESHOLD} bpm, duration: ${config.HIGH_HEART_RATE_DURATION / 1000}s`);
    }

    /**
     * 心拍数データを処理する
     * @param {number} heartRate - 心拍数
     */
    async processHeartRate(heartRate) {
        this.lastHeartRate = heartRate;
        const now = new Date();

        if (heartRate >= config.HEART_RATE_THRESHOLD) {
            await this.handleHighHeartRate(heartRate, now);
        } else {
            await this.handleNormalHeartRate(heartRate, now);
        }
    }

    /**
     * 高心拍数の処理
     * @param {number} heartRate - 心拍数
     * @param {Date} now - 現在時刻
     */
    async handleHighHeartRate(heartRate, now) {
        if (!this.isHighHeartRate) {
            // 高心拍数状態の開始
            this.isHighHeartRate = true;
            this.highHeartRateStartTime = now;
            console.log(`High heart rate detected: ${heartRate} bpm (threshold: ${config.HEART_RATE_THRESHOLD} bpm)`);
        } else {
            // 高心拍数状態の継続
            const duration = now - this.highHeartRateStartTime;
            
            if (duration >= config.HIGH_HEART_RATE_DURATION) {
                await this.checkAndSendNotification(heartRate, duration, now);
            }
        }
    }

    /**
     * 正常心拍数の処理
     * @param {number} heartRate - 心拍数
     * @param {Date} now - 現在時刻
     */
    async handleNormalHeartRate(heartRate, now) {
        if (this.isHighHeartRate) {
            // 高心拍数状態の終了
            const totalDuration = now - this.highHeartRateStartTime;
            console.log(`Heart rate normalized: ${heartRate} bpm (was high for ${Math.round(totalDuration / 1000)}s)`);
            
            // 正常化通知を送信（高心拍数が一定時間以上続いていた場合のみ）
            if (totalDuration >= config.HIGH_HEART_RATE_DURATION) {
                await this.sendNormalizationNotification(heartRate, totalDuration, now);
            }
            
            this.isHighHeartRate = false;
            this.highHeartRateStartTime = null;
        }
    }

    /**
     * 通知送信の判定と実行
     * @param {number} heartRate - 心拍数
     * @param {number} duration - 継続時間
     * @param {Date} now - 現在時刻
     */
    async checkAndSendNotification(heartRate, duration, now) {
        // スパム対策チェック
        if (!this.canSendNotification(now)) {
            console.log("Notification skipped due to spam protection");
            return;
        }

        // 通知送信
        await sendDiscordNotification({
            heartRate,
            threshold: config.HEART_RATE_THRESHOLD,
            duration,
            startTime: this.highHeartRateStartTime
        });

        // 通知履歴を更新
        this.updateNotificationHistory(now);
    }

    /**
     * 正常化通知の送信
     * @param {number} heartRate - 心拍数
     * @param {number} totalDuration - 総継続時間
     * @param {Date} endTime - 終了時刻
     */
    async sendNormalizationNotification(heartRate, totalDuration, endTime) {
        const canSend = spamProtection.canSendNotification('heart_rate_normalized', endTime);
        if (canSend.allowed) {
            await sendHeartRateNormalizedNotification({
                heartRate,
                threshold: config.HEART_RATE_THRESHOLD,
                totalDuration,
                startTime: this.highHeartRateStartTime,
                endTime
            });
            spamProtection.recordNotification('heart_rate_normalized', endTime);
        } else {
            console.log(`Normalization notification blocked: ${canSend.reason}`);
        }
    }

    /**
     * 通知送信可能かチェック（スパム対策）
     * @param {Date} now - 現在時刻
     * @returns {boolean} 送信可能かどうか
     */
    canSendNotification(now) {
        const result = spamProtection.canSendNotification('high_heart_rate', now);
        if (!result.allowed) {
            console.log(`Notification blocked: ${result.reason}${result.remainingSeconds ? ` (${result.remainingSeconds}s remaining)` : ''}`);
        }
        return result.allowed;
    }

    /**
     * 通知履歴を更新
     * @param {Date} now - 現在時刻
     */
    updateNotificationHistory(now) {
        spamProtection.recordNotification('high_heart_rate', now);
        this.lastNotificationTime = now;
    }

    /**
     * 現在の監視状態を取得
     * @returns {Object} 監視状態
     */
    getStatus() {
        const spamStatus = spamProtection.getStatus();
        return {
            isHighHeartRate: this.isHighHeartRate,
            highHeartRateStartTime: this.highHeartRateStartTime,
            lastHeartRate: this.lastHeartRate,
            lastNotificationTime: this.lastNotificationTime,
            threshold: config.HEART_RATE_THRESHOLD,
            requiredDuration: config.HIGH_HEART_RATE_DURATION,
            spamProtection: spamStatus
        };
    }
}

// シングルトンインスタンス
const heartRateMonitor = new HeartRateMonitor();

module.exports = {
    HeartRateMonitor,
    heartRateMonitor
};
