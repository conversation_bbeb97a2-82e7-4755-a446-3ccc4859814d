/**
 * スパム対策サービス
 * 通知の頻度制限、クールダウン機能、重複通知防止機能を提供
 */

const { config } = require('../config');

/**
 * スパム対策クラス
 */
class SpamProtection {
    constructor() {
        // 通知履歴の管理
        this.notificationHistory = new Map(); // key: notificationType, value: Array of timestamps
        this.lastNotificationTimes = new Map(); // key: notificationType, value: last notification timestamp
        
        // レート制限の設定
        this.rateLimits = {
            'high_heart_rate': {
                cooldown: config.NOTIFICATION_COOLDOWN,
                maxPerHour: config.MAX_NOTIFICATIONS_PER_HOUR
            },
            'heart_rate_normalized': {
                cooldown: 300000, // 5分
                maxPerHour: 6 // 1時間に6回まで
            },
            'system_alert': {
                cooldown: 600000, // 10分
                maxPerHour: 3 // 1時間に3回まで
            }
        };
        
        console.log("Spam protection initialized");
    }

    /**
     * 通知送信可能かチェック
     * @param {string} notificationType - 通知タイプ
     * @param {Date} now - 現在時刻
     * @returns {Object} チェック結果
     */
    canSendNotification(notificationType, now = new Date()) {
        const limits = this.rateLimits[notificationType];
        if (!limits) {
            console.warn(`Unknown notification type: ${notificationType}`);
            return { allowed: false, reason: 'Unknown notification type' };
        }

        // クールダウン期間チェック
        const cooldownCheck = this.checkCooldown(notificationType, now, limits.cooldown);
        if (!cooldownCheck.allowed) {
            return cooldownCheck;
        }

        // 1時間あたりの通知数制限チェック
        const rateCheck = this.checkRateLimit(notificationType, now, limits.maxPerHour);
        if (!rateCheck.allowed) {
            return rateCheck;
        }

        return { allowed: true };
    }

    /**
     * クールダウン期間をチェック
     * @param {string} notificationType - 通知タイプ
     * @param {Date} now - 現在時刻
     * @param {number} cooldownMs - クールダウン時間（ミリ秒）
     * @returns {Object} チェック結果
     */
    checkCooldown(notificationType, now, cooldownMs) {
        const lastTime = this.lastNotificationTimes.get(notificationType);
        if (lastTime) {
            const timeSinceLastNotification = now - lastTime;
            if (timeSinceLastNotification < cooldownMs) {
                const remainingTime = Math.ceil((cooldownMs - timeSinceLastNotification) / 1000);
                return {
                    allowed: false,
                    reason: 'Cooldown period',
                    remainingSeconds: remainingTime
                };
            }
        }
        return { allowed: true };
    }

    /**
     * レート制限をチェック
     * @param {string} notificationType - 通知タイプ
     * @param {Date} now - 現在時刻
     * @param {number} maxPerHour - 1時間あたりの最大通知数
     * @returns {Object} チェック結果
     */
    checkRateLimit(notificationType, now, maxPerHour) {
        const oneHourAgo = new Date(now.getTime() - 3600000);
        let history = this.notificationHistory.get(notificationType) || [];
        
        // 1時間以内の通知をフィルタリング
        history = history.filter(time => time > oneHourAgo);
        this.notificationHistory.set(notificationType, history);
        
        if (history.length >= maxPerHour) {
            const oldestNotification = Math.min(...history);
            const resetTime = new Date(oldestNotification.getTime() + 3600000);
            const remainingTime = Math.ceil((resetTime - now) / 1000);
            
            return {
                allowed: false,
                reason: 'Rate limit exceeded',
                remainingSeconds: remainingTime,
                currentCount: history.length,
                maxCount: maxPerHour
            };
        }
        
        return { allowed: true };
    }

    /**
     * 通知送信を記録
     * @param {string} notificationType - 通知タイプ
     * @param {Date} now - 現在時刻
     */
    recordNotification(notificationType, now = new Date()) {
        // 最後の通知時刻を更新
        this.lastNotificationTimes.set(notificationType, now);
        
        // 通知履歴に追加
        let history = this.notificationHistory.get(notificationType) || [];
        history.push(now);
        this.notificationHistory.set(notificationType, history);
        
        // 古い履歴を削除（24時間以上前）
        this.cleanupOldHistory(notificationType, now);
        
        console.log(`Notification recorded: ${notificationType} at ${now.toISOString()}`);
    }

    /**
     * 古い履歴を削除
     * @param {string} notificationType - 通知タイプ
     * @param {Date} now - 現在時刻
     */
    cleanupOldHistory(notificationType, now) {
        const oneDayAgo = new Date(now.getTime() - 86400000);
        let history = this.notificationHistory.get(notificationType) || [];
        history = history.filter(time => time > oneDayAgo);
        this.notificationHistory.set(notificationType, history);
    }

    /**
     * 特定タイプの通知履歴をクリア
     * @param {string} notificationType - 通知タイプ
     */
    clearHistory(notificationType) {
        this.notificationHistory.delete(notificationType);
        this.lastNotificationTimes.delete(notificationType);
        console.log(`Cleared history for notification type: ${notificationType}`);
    }

    /**
     * 全ての通知履歴をクリア
     */
    clearAllHistory() {
        this.notificationHistory.clear();
        this.lastNotificationTimes.clear();
        console.log("Cleared all notification history");
    }

    /**
     * 現在の状態を取得
     * @returns {Object} 現在の状態
     */
    getStatus() {
        const status = {
            rateLimits: this.rateLimits,
            notificationCounts: {},
            lastNotificationTimes: {}
        };

        // 各通知タイプの現在の状態を取得
        for (const [type, history] of this.notificationHistory.entries()) {
            const oneHourAgo = new Date(Date.now() - 3600000);
            const recentNotifications = history.filter(time => time > oneHourAgo);
            status.notificationCounts[type] = recentNotifications.length;
        }

        for (const [type, time] of this.lastNotificationTimes.entries()) {
            status.lastNotificationTimes[type] = time.toISOString();
        }

        return status;
    }

    /**
     * 次回通知可能時刻を取得
     * @param {string} notificationType - 通知タイプ
     * @returns {Date|null} 次回通知可能時刻
     */
    getNextAvailableTime(notificationType) {
        const limits = this.rateLimits[notificationType];
        if (!limits) return null;

        const now = new Date();
        const cooldownCheck = this.checkCooldown(notificationType, now, limits.cooldown);
        const rateCheck = this.checkRateLimit(notificationType, now, limits.maxPerHour);

        if (cooldownCheck.allowed && rateCheck.allowed) {
            return now; // 今すぐ送信可能
        }

        // クールダウンまたはレート制限により送信不可の場合
        const cooldownTime = cooldownCheck.allowed ? 0 : cooldownCheck.remainingSeconds * 1000;
        const rateTime = rateCheck.allowed ? 0 : rateCheck.remainingSeconds * 1000;
        
        const waitTime = Math.max(cooldownTime, rateTime);
        return new Date(now.getTime() + waitTime);
    }
}

// シングルトンインスタンス
const spamProtection = new SpamProtection();

module.exports = {
    SpamProtection,
    spamProtection
};
