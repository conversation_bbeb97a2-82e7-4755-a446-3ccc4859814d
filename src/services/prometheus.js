const { config } = require('../config');

/**
 * 心拍数をPrometheus/VictoriaMetricsに送信する関数
 * @param {number} heartRate - 送信する心拍数
 */
async function sendHeartRateToPrometheus(heartRate) {
    const payload = `heart_rate value=${heartRate}`;

    try {
        const response = await fetch(config.VICTORIAMETRICS_URL, {
            method: "POST",
            headers: {
                "Content-Type": "text/plain",
            },
            body: payload,
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        console.log(`Successfully sent heart rate ${heartRate} bpm to Prometheus`);
    } catch (error) {
        console.error("Error sending heart rate to Prometheus:", error);
    }
}

module.exports = {
    sendHeartRateToPrometheus
};
