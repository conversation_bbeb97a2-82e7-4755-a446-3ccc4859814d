/**
 * 監視ユーティリティ
 * 心拍数監視システムの状態確認や管理機能を提供
 */

const { heartRateMonitor } = require('../services/heartRateMonitor');
const { spamProtection } = require('../services/spamProtection');
const { config } = require('../config');

/**
 * 監視システムの詳細状態を取得
 * @returns {Object} 詳細状態情報
 */
function getDetailedStatus() {
    const monitorStatus = heartRateMonitor.getStatus();
    const spamStatus = spamProtection.getStatus();
    
    return {
        timestamp: new Date().toISOString(),
        heartRateMonitor: {
            ...monitorStatus,
            currentDuration: monitorStatus.isHighHeartRate && monitorStatus.highHeartRateStartTime 
                ? Date.now() - monitorStatus.highHeartRateStartTime.getTime()
                : 0,
            timeUntilNotification: monitorStatus.isHighHeartRate && monitorStatus.highHeartRateStartTime
                ? Math.max(0, config.HIGH_HEART_RATE_DURATION - (Date.now() - monitorStatus.highHeartRateStartTime.getTime()))
                : 0
        },
        spamProtection: spamStatus,
        configuration: {
            threshold: config.HEART_RATE_THRESHOLD,
            requiredDuration: config.HIGH_HEART_RATE_DURATION,
            notificationCooldown: config.NOTIFICATION_COOLDOWN,
            maxNotificationsPerHour: config.MAX_NOTIFICATIONS_PER_HOUR,
            discordConfigured: !!(config.DISCORD_WEBHOOK_URL && config.DISCORD_WEBHOOK_URL !== "YOUR_DISCORD_WEBHOOK_URL_HERE")
        }
    };
}

/**
 * 監視システムの簡易状態を取得
 * @returns {Object} 簡易状態情報
 */
function getSimpleStatus() {
    const status = getDetailedStatus();
    
    return {
        isMonitoring: true,
        isHighHeartRate: status.heartRateMonitor.isHighHeartRate,
        lastHeartRate: status.heartRateMonitor.lastHeartRate,
        threshold: status.configuration.threshold,
        discordConfigured: status.configuration.discordConfigured,
        canSendNotification: spamProtection.canSendNotification('high_heart_rate').allowed
    };
}

/**
 * 次回通知可能時刻を取得
 * @returns {Object} 通知可能時刻情報
 */
function getNextNotificationTimes() {
    return {
        highHeartRate: spamProtection.getNextAvailableTime('high_heart_rate'),
        normalized: spamProtection.getNextAvailableTime('heart_rate_normalized'),
        systemAlert: spamProtection.getNextAvailableTime('system_alert')
    };
}

/**
 * 監視統計を取得
 * @returns {Object} 統計情報
 */
function getMonitoringStats() {
    const spamStatus = spamProtection.getStatus();
    
    return {
        notificationCounts: spamStatus.notificationCounts,
        lastNotificationTimes: spamStatus.lastNotificationTimes,
        rateLimits: spamStatus.rateLimits
    };
}

/**
 * 設定の妥当性をチェック
 * @returns {Object} 設定チェック結果
 */
function validateConfiguration() {
    const issues = [];
    const warnings = [];
    
    // Discord設定チェック
    if (!config.DISCORD_WEBHOOK_URL || config.DISCORD_WEBHOOK_URL === "YOUR_DISCORD_WEBHOOK_URL_HERE") {
        warnings.push("Discord webhook URL is not configured. Notifications will be skipped.");
    } else if (!config.DISCORD_WEBHOOK_URL.startsWith("https://discord.com/api/webhooks/")) {
        issues.push("Discord webhook URL format is invalid.");
    }
    
    // 閾値設定チェック
    if (config.HEART_RATE_THRESHOLD <= 0) {
        issues.push("Heart rate threshold must be a positive number.");
    } else if (config.HEART_RATE_THRESHOLD < 60) {
        warnings.push("Heart rate threshold is very low (< 60 bpm). This may cause frequent notifications.");
    } else if (config.HEART_RATE_THRESHOLD > 200) {
        warnings.push("Heart rate threshold is very high (> 200 bpm). This may miss important alerts.");
    }
    
    // 継続時間設定チェック
    if (config.HIGH_HEART_RATE_DURATION <= 0) {
        issues.push("High heart rate duration must be a positive number.");
    } else if (config.HIGH_HEART_RATE_DURATION < 60000) { // 1分未満
        warnings.push("High heart rate duration is very short (< 1 minute). This may cause frequent notifications.");
    }
    
    // クールダウン設定チェック
    if (config.NOTIFICATION_COOLDOWN < config.HIGH_HEART_RATE_DURATION) {
        warnings.push("Notification cooldown is shorter than required duration. This may cause rapid notifications.");
    }
    
    return {
        isValid: issues.length === 0,
        issues,
        warnings,
        configuration: {
            threshold: config.HEART_RATE_THRESHOLD,
            duration: config.HIGH_HEART_RATE_DURATION,
            cooldown: config.NOTIFICATION_COOLDOWN,
            maxPerHour: config.MAX_NOTIFICATIONS_PER_HOUR
        }
    };
}

/**
 * 監視システムをリセット
 */
function resetMonitoringSystem() {
    spamProtection.clearAllHistory();
    console.log("Monitoring system has been reset");
}

/**
 * 特定の通知タイプの履歴をクリア
 * @param {string} notificationType - 通知タイプ
 */
function clearNotificationHistory(notificationType) {
    spamProtection.clearHistory(notificationType);
    console.log(`Cleared history for notification type: ${notificationType}`);
}

/**
 * 監視状態をコンソールに出力
 */
function logCurrentStatus() {
    const status = getDetailedStatus();
    
    console.log("\n=== Heart Rate Monitoring Status ===");
    console.log(`Timestamp: ${status.timestamp}`);
    console.log(`Last Heart Rate: ${status.heartRateMonitor.lastHeartRate || 'N/A'} bpm`);
    console.log(`Threshold: ${status.configuration.threshold} bpm`);
    console.log(`High Heart Rate: ${status.heartRateMonitor.isHighHeartRate ? 'YES' : 'NO'}`);
    
    if (status.heartRateMonitor.isHighHeartRate) {
        console.log(`Duration: ${Math.round(status.heartRateMonitor.currentDuration / 1000)}s`);
        console.log(`Time until notification: ${Math.round(status.heartRateMonitor.timeUntilNotification / 1000)}s`);
    }
    
    console.log(`Discord Configured: ${status.configuration.discordConfigured ? 'YES' : 'NO'}`);
    console.log(`Can Send Notification: ${spamProtection.canSendNotification('high_heart_rate').allowed ? 'YES' : 'NO'}`);
    
    // 通知統計
    const stats = status.spamProtection.notificationCounts;
    if (Object.keys(stats).length > 0) {
        console.log("\nNotification Counts (last hour):");
        for (const [type, count] of Object.entries(stats)) {
            console.log(`  ${type}: ${count}`);
        }
    }
    
    console.log("=====================================\n");
}

module.exports = {
    getDetailedStatus,
    getSimpleStatus,
    getNextNotificationTimes,
    getMonitoringStats,
    validateConfiguration,
    resetMonitoringSystem,
    clearNotificationHistory,
    logCurrentStatus
};
