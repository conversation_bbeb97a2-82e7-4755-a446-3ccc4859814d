/**
 * メッセージパーサーユーティリティ
 * Pulsoidから受信したメッセージの解析を行う
 */

/**
 * 受信したメッセージから心拍数を抽出する関数
 * @param {Buffer} message - 受信したメッセージ
 * @returns {number|undefined} 抽出された心拍数、または undefined
 */
function parseHeartRateFromMessage(message) {
    try {
        const messageString = message.toString();
        console.log('Received raw message:', messageString);

        let heartRate;

        // パターンA: 数値のみの場合（例: "75"）
        const parsedAsNumber = parseFloat(messageString);

        if (!isNaN(parsedAsNumber)) {
            heartRate = parsedAsNumber;
        } else {
            // JSONとしてパースを試行
            try {
                const data = JSON.parse(messageString);
                if (data && data.data && data.data.heart_rate) {
                    // パターンB: {"data": {"heart_rate": 75}}
                    heartRate = data.data.heart_rate;
                } else if (data && data.heart_rate) {
                    // パターンC: {"heart_rate": 75}
                    heartRate = data.heart_rate;
                }
            } catch (jsonError) {
                // JSONとしてもパースできない場合は何もしない
                // heartRateはundefinedのままになる
            }
        }

        if (heartRate !== undefined) {
            console.log(`Extracted Heart Rate: ${heartRate} bpm`);
        } else {
            console.log("Could not extract heart rate from message.");
        }

        return heartRate;
    } catch (error) {
        console.error("Error parsing message:", error);
        return undefined;
    }
}

module.exports = {
    parseHeartRateFromMessage
};
